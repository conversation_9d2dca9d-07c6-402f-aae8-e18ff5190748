from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import logging
import uvicorn
import json
import psycopg2
import re
import uuid
import datetime
from pytz import timezone
from chatbot_implementation import (
    chatbot_response, get_collection_name
)
import requests
from caf_integration import (
    handle_caf_number_input,
    handle_otp_verification,
    handle_service_selection,
    format_services_list
)

# --- Logging ---
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- FastAPI App ---
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Timezone ---
tz = timezone('Asia/Kolkata')

def json_load_rag()->list:
    with open("rag_link_2.json", "r") as f:
        data = json.load(f)
    return data

def rag_api(link_data, question):
    url = "https://wbgw.napix.gov.in/wb/coeailabkol/web_rag"

    payload = json.dumps({
        "params": {
            "key": "Ailab@123",
            "job_id": "5",
            "task_id":"1",
            "input_lst": link_data,
            "qus_str": question,
            "no_of_ques": "NA"
            }
            }
            )
    headers = {
    'client_id': '7d84dff9e324f19a65ebf32c9fe0030a',
    'client_secret': '57ec1c37c60454b31f5494913a048d81',
    'Content-Type': 'application/json'
    }
    r = requests.request("POST", url, headers=headers, data=payload)
    print(r.status_code)
    if r.status_code == 200:
        json_res=json.loads(r.content.decode('utf-8'))
    else:
        json_res= {'status': 'success', 'message': 'No response found', 'job_id': '5', 'question': [], 'answer': ["No response found at this moment please try again later."], 'urls': ['https://example.com'], 'discarded_urls': []}

    return json_res

# --- PostgreSQL Connection ---
def get_pg_conn():
    return psycopg2.connect(
        database="user_session_eodb_data_DB",
        user="postgres",
        password="padmin",
        host="***********",
        port="5432"
    )

# --- Session DB Operations ---
def store_session(session_id, sector, investment):
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        # Create table if it doesn't exist
        cur.execute("""
            CREATE TABLE IF NOT EXISTS user_session_eodb_data_tab (
                session_id VARCHAR PRIMARY KEY,
                sector VARCHAR,
                investment VARCHAR,
                selected_service VARCHAR,
                time TIMESTAMP DEFAULT NOW()
            );
        """)

        # Add selected_service column if it doesn't exist (for existing tables)
        try:
            cur.execute("""
                ALTER TABLE user_session_eodb_data_tab
                ADD COLUMN IF NOT EXISTS selected_service VARCHAR;
            """)
        except Exception as alter_error:
            # Column might already exist, that's fine
            logger.info(f"Column selected_service might already exist: {alter_error}")

        # Insert or update session data
        cur.execute("""
            INSERT INTO user_session_eodb_data_tab (session_id, sector, investment, time)
            VALUES (%s, %s, %s, NOW())
            ON CONFLICT (session_id) DO UPDATE SET sector=EXCLUDED.sector, investment=EXCLUDED.investment, time=NOW();
        """, (session_id, sector, investment))
        conn.commit()
    except Exception as e:
        logger.error(f"PostgreSQL error: {e}")
    finally:
        conn.close()

def store_selected_service(session_id, service):
    """Store selected service in session data"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        # Create table if it doesn't exist
        cur.execute("""
            CREATE TABLE IF NOT EXISTS user_session_eodb_data_tab (
                session_id VARCHAR PRIMARY KEY,
                sector VARCHAR,
                investment VARCHAR,
                selected_service VARCHAR,
                time TIMESTAMP DEFAULT NOW()
            );
        """)
        
        # Add selected_service column if it doesn't exist
        try:
            cur.execute("""
                ALTER TABLE user_session_eodb_data_tab
                ADD COLUMN IF NOT EXISTS selected_service VARCHAR;
            """)
        except Exception as alter_error:
            logger.info(f"Column selected_service might already exist: {alter_error}")
        
        conn.commit()
        
        # Update the selected_service for existing session
        cur.execute("""
            UPDATE user_session_eodb_data_tab
            SET selected_service = %s, time = NOW()
            WHERE session_id = %s
        """, (service, session_id))

        # If no rows were updated, insert a new record
        if cur.rowcount == 0:
            cur.execute("""
                INSERT INTO user_session_eodb_data_tab (session_id, selected_service, time)
                VALUES (%s, %s, NOW())
            """, (session_id, service))

        conn.commit()
    except Exception as e:
        logger.error(f"PostgreSQL error storing selected service: {e}")
    finally:
        conn.close()

def get_session(session_id):
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        # Create table if it doesn't exist (same as in store_session)
        cur.execute("""
            CREATE TABLE IF NOT EXISTS user_session_eodb_data_tab (
                session_id VARCHAR PRIMARY KEY,
                sector VARCHAR,
                investment VARCHAR,
                selected_service VARCHAR,
                time TIMESTAMP DEFAULT NOW()
            );
        """)
        
        # Add selected_service column if it doesn't exist (for existing tables)
        try:
            cur.execute("""
                ALTER TABLE user_session_eodb_data_tab
                ADD COLUMN IF NOT EXISTS selected_service VARCHAR;
            """)
        except Exception as alter_error:
            # Column might already exist, that's fine
            logger.info(f"Column selected_service might already exist: {alter_error}")
        
        conn.commit()
        
        # Now query the session data
        cur.execute("""
            SELECT sector, investment, selected_service FROM user_session_eodb_data_tab
            WHERE session_id = %s
        """, (session_id,))
        result = cur.fetchone()
        if result:
            return {"sector": result[0], "investment": result[1], "selected_service": result[2]}
        return None
    except Exception as e:
        logger.error(f"PostgreSQL error: {e}")
        return None
    finally:
        conn.close()

def get_selected_service(session_id):
    """Get selected service from session data"""
    session_data = get_session(session_id)
    if session_data and session_data.get("selected_service"):
        return session_data["selected_service"]
    return None

def handle_navigation_action(session_id: str, action: str) -> Dict[str, Any]:
    """Handle Previous Menu and Main Menu navigation actions"""
    if action == "Previous Menu":
        # Go back to Step 3 (Application Type Selection)
        # Clear service-specific session data but keep sector/investment
        clear_service_session_data(session_id)

        # Get sector and investment from session for step 3
        session_data = get_session(session_id)
        sector = session_data.get('sector', '') if session_data else None
        investment = session_data.get('investment', '') if session_data else None

        if sector and investment:
            # Get dynamic options for step 3
            options = get_options_by_step(3)
            button_list = format_options_as_buttons(options)
            caption = get_step_caption(3)

            return {
                "session_id": session_id,
                "intent_id": "103",
                "intent_name": "choose_action",
                "response": caption,
                "response_type": "options",
                "option_list": button_list,
                "followup_yes": "NA",
                "followup_no": "NA",
                "step": 3
            }
        else:
            # If no sector/investment, go to step 1
            return handle_main_menu_action(session_id)
    elif action == "Main Menu":
        return handle_main_menu_action(session_id)

    return None

def handle_main_menu_action(session_id: str) -> Dict[str, Any]:
    """Handle Main Menu action - go back to Step 1 (Sector Selection)"""
    # Clear all session data except session_id
    clear_all_session_data(session_id)

    # Return to sector selection using dynamic data
    options = get_options_by_step(1)
    button_list = format_options_as_buttons(options)
    caption = get_step_caption(1)

    return {
        "session_id": session_id,
        "intent_id": "101",
        "intent_name": "choose_sector",
        "response": caption,
        "response_type": "options",
        "option_list": button_list,
        "followup_yes": "NA",
        "followup_no": "NA",
        "step": 1
    }

def clear_service_session_data(session_id: str):
    """Clear service-specific session data while keeping sector/investment"""
    try:
        # Get current session data
        session_data = get_session(session_id)
        if session_data:
            sector = session_data.get("sector")
            investment = session_data.get("investment")

            # Clear session data from PostgreSQL
            conn = get_pg_conn()
            cur = conn.cursor()
            try:
                # Delete the session record
                cur.execute("DELETE FROM user_session_eodb_data_tab WHERE session_id = %s", (session_id,))
                conn.commit()

                # Restore sector and investment for Step 4
                if sector and investment:
                    store_session(session_id, sector, investment)

            except Exception as db_error:
                logger.error(f"Database error clearing service session: {db_error}")
                conn.rollback()
            finally:
                conn.close()

        # Clear additional session data
        if session_id in session_data_store:
            # Keep sector and investment if they exist
            sector_backup = session_data_store[session_id].get("sector")
            investment_backup = session_data_store[session_id].get("investment")

            # Clear all data
            session_data_store[session_id].clear()

            # Restore sector and investment
            if sector_backup:
                session_data_store[session_id]["sector"] = sector_backup
            if investment_backup:
                session_data_store[session_id]["investment"] = investment_backup

    except Exception as e:
        logger.error(f"Error clearing service session data: {e}")

def clear_all_session_data(session_id: str):
    """Clear all session data for main menu navigation"""
    try:
        # Clear session data from PostgreSQL
        conn = get_pg_conn()
        cur = conn.cursor()
        try:
            # Delete the session record
            cur.execute("DELETE FROM user_session_eodb_data_tab WHERE session_id = %s", (session_id,))
            conn.commit()
        except Exception as db_error:
            logger.error(f"Database error clearing all session: {db_error}")
            conn.rollback()
        finally:
            conn.close()

        # Clear additional session data
        if session_id in session_data_store:
            session_data_store[session_id].clear()

    except Exception as e:
        logger.error(f"Error clearing all session data: {e}")

def should_show_navigation_buttons(step: int, application_type: str = None) -> bool:
    """Determine if navigation buttons should be shown"""
    # Show navigation buttons only after user selects the licence/clearance application option
    # Get the licence/clearance option dynamically from database
    licence_option = get_dynamic_option_by_pattern('application_types', 'licence/clearance')
    return (step >= 4 and application_type == licence_option)

def get_navigation_buttons_html(step: int = 1, application_type: str = None) -> str:
    """Get navigation buttons HTML if they should be shown"""
    if not should_show_navigation_buttons(step, application_type):
        return ""

    # Navigation buttons with same color as chatbot options, positioned beside each other after dislike button
    navigation_html = '''<button type="button" class="nav-btn" onclick="sendMessage('Previous Menu')" style="background: #007bff; color: white; border: none; padding: 8px 12px; margin: 0 2px; border-radius: 4px; font-size: 14px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'" title="Previous Menu">←</button><button type="button" class="nav-btn" onclick="sendMessage('Main Menu')" style="background: #007bff; color: white; border: none; padding: 8px 12px; margin: 0 2px; border-radius: 4px; font-size: 14px; cursor: pointer; transition: background 0.3s ease;" onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'" title="Main Menu">🏠</button>'''

    return navigation_html

##--- API Models ---
class UserResponse(BaseModel):
    caption: str
    value: str

class ChatRequest(BaseModel):
    session_id: Optional[str] = None
    collection_name: Optional[str] = None  # Made optional for stepwise flow
    user_input: Optional[str] = ""  # Made optional for stepwise flow
    step: Optional[int] = 1
    user_response: Optional[UserResponse] = None
    response_type: Optional[str] = None
    followup_yes: Optional[str] = None
    followup_no: Optional[str] = None
    criteria_list: Optional[List] = []

class SessionIdGenerator(BaseModel):
    session_type: str
    session_id: Optional[str] = None

##--- Dynamic Database Functions ---
def get_master_data_by_type(type_name: str):
    """Get all active options for a specific type from database"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT md.value
            FROM eodb_master_data_final md
            JOIN eodb_master_data_types_final mdt ON md.type_id = mdt.id
            WHERE mdt.type_name = %s AND md.is_active = TRUE
            ORDER BY md.order_num, md.value
        """, (type_name,))

        results = cur.fetchall()
        return [row[0] for row in results]
    except Exception as e:
        logger.error(f"Error fetching master data for {type_name}: {e}")
        return []
    finally:
        conn.close()

def get_step_flow_config(step_number: int, dependent_on_step: int = None, dependent_on_value: str = None):
    """Get step flow configuration from database"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        if dependent_on_step and dependent_on_value:
            cur.execute("""
                SELECT sf.step_number, sf.response_type, sf.input_caption, sf.next_step,
                       mdt.type_name, mdt.display_name
                FROM eodb_step_flow_final sf
                JOIN eodb_master_data_types_final mdt ON sf.type_id = mdt.id
                WHERE sf.step_number = %s AND sf.dependent_on_step = %s AND sf.dependent_on_value = %s
            """, (step_number, dependent_on_step, dependent_on_value))
        else:
            cur.execute("""
                SELECT sf.step_number, sf.response_type, sf.input_caption, sf.next_step,
                       mdt.type_name, mdt.display_name
                FROM eodb_step_flow_final sf
                JOIN eodb_master_data_types_final mdt ON sf.type_id = mdt.id
                WHERE sf.step_number = %s AND sf.dependent_on_step IS NULL
            """, (step_number,))

        result = cur.fetchone()
        if result:
            return {
                "step_number": result[0],
                "response_type": result[1],
                "input_caption": result[2],
                "next_step": result[3],
                "type_name": result[4],
                "display_name": result[5]
            }
        return None
    except Exception as e:
        logger.error(f"Error fetching step flow config for step {step_number}: {e}")
        return None
    finally:
        conn.close()

def get_options_by_step(step_number: int, dependent_on_step: int = None, dependent_on_value: str = None):
    """Get options for a specific step from database"""
    step_config = get_step_flow_config(step_number, dependent_on_step, dependent_on_value)
    if step_config and step_config["type_name"]:
        return get_master_data_by_type(step_config["type_name"])
    return []

def get_step_caption(step_number: int, dependent_on_step: int = None, dependent_on_value: str = None):
    """Get step caption from database"""
    step_config = get_step_flow_config(step_number, dependent_on_step, dependent_on_value)
    if step_config:
        return step_config["input_caption"]
    return "Please select an option:"

def is_option_value_match(option_value: str, comparison_values: list):
    """Check if option value matches any of the comparison values dynamically"""
    return option_value in comparison_values

def get_application_type_options():
    """Get application type options from database"""
    return get_master_data_by_type('application_types')

def get_service_type_options():
    """Get service type options from database"""
    return get_master_data_by_type('service_types')

def get_sectors():
    """Get sectors from database"""
    return get_master_data_by_type('sectors')

def get_investments():
    """Get investments from database"""
    return get_master_data_by_type('investments')

def get_pre_establishment():
    """Get pre-establishment services from database"""
    return get_master_data_by_type('pre_establishment')

def get_pre_operation():
    """Get pre-operation services from database"""
    return get_master_data_by_type('pre_operation')

def get_dynamic_option_by_pattern(type_name: str, pattern: str):
    """Get specific option that matches a pattern from database"""
    options = get_master_data_by_type(type_name)
    for option in options:
        if pattern.lower() in option.lower():
            return option
    return None

def format_options_as_buttons(options_list):
    """Convert a list of options to HTML button elements"""
    button_list = []
    for option in options_list:
        # Create a safe value for the button ID (remove spaces, special chars)
        safe_id = re.sub(r'[^a-zA-Z0-9]', '_', option.lower())
        button_list.append(f"<button type='button' id='{safe_id}_btn' class='option_btn {safe_id}_btn' value='{option}' onclick='selectPythonApiOption(\"{option}\")'>{option}</button>")

    # Navigation buttons are handled separately and added beside like/dislike buttons
    return button_list

# Session data storage (in production, use Redis or database)
session_data_store = {}

def store_session_data(session_id, data):
    """Store additional session data"""
    if session_id not in session_data_store:
        session_data_store[session_id] = {}
    session_data_store[session_id].update(data)

def get_session_data(session_id, key=None):
    """Get session data"""
    if session_id not in session_data_store:
        return None
    if key:
        return session_data_store[session_id].get(key)
    return session_data_store[session_id]

def clear_session_data(session_id):
    """Clear all session data for a session"""
    if session_id in session_data_store:
        session_data_store[session_id].clear()

    # Also clear from database
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("DELETE FROM user_session_eodb_data_tab WHERE session_id = %s", (session_id,))
        conn.commit()
    except Exception as e:
        logger.error(f"Error clearing session data from database: {e}")
        conn.rollback()
    finally:
        conn.close()

def format_services_list(services):
    """Format services list for display"""
    if not services:
        return "No services found."

    formatted_list = []
    for i, service in enumerate(services, 1):
        if isinstance(service, dict):
            service_name = service.get('service_name', 'Unknown Service')
            status = service.get('status', 'Unknown Status')
            formatted_list.append(f"{i}. {service_name} - Status: {status}")
        else:
            formatted_list.append(f"{i}. {service}")

    return "<br>".join(formatted_list)

# --- Dynamic Configuration Functions ---

def get_system_config(config_key: str, default_value: str = ""):
    """Get system configuration value from database"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT config_value, config_type
            FROM eodb_system_config_final
            WHERE config_key = %s AND is_active = TRUE
        """, (config_key,))

        result = cur.fetchone()
        if result:
            value, config_type = result
            # Convert based on type
            if config_type == 'boolean':
                return value.lower() in ('true', '1', 'yes')
            elif config_type == 'number':
                return int(value) if value.isdigit() else float(value)
            elif config_type == 'json':
                return json.loads(value)
            else:
                return value
        return default_value
    except Exception as e:
        logger.error(f"Error getting system config {config_key}: {e}")
        return default_value
    finally:
        conn.close()

def get_greeting_message(message_key: str, default_message: str = ""):
    """Get greeting message from database"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT message_text
            FROM eodb_greeting_messages_final
            WHERE message_key = %s AND is_active = TRUE
        """, (message_key,))

        result = cur.fetchone()
        return result[0] if result else default_message
    except Exception as e:
        logger.error(f"Error getting greeting message {message_key}: {e}")
        return default_message
    finally:
        conn.close()

def get_keyword_redirect(user_input: str):
    """Check if user input matches any keyword redirect"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT keyword, redirect_to_step, redirect_to_option, redirect_message, is_case_sensitive
            FROM eodb_keyword_redirects_final
            WHERE is_active = TRUE
            ORDER BY priority DESC, LENGTH(keyword) DESC
        """)

        results = cur.fetchall()
        user_input_lower = user_input.lower()

        for keyword, redirect_step, redirect_option, redirect_message, is_case_sensitive in results:
            search_text = keyword if is_case_sensitive else keyword.lower()
            search_input = user_input if is_case_sensitive else user_input_lower

            if search_text in search_input:
                return {
                    "keyword": keyword,
                    "redirect_to_step": redirect_step,
                    "redirect_to_option": redirect_option,
                    "redirect_message": redirect_message
                }
        return None
    except Exception as e:
        logger.error(f"Error checking keyword redirects: {e}")
        return None
    finally:
        conn.close()

def get_special_command(command: str):
    """Get special command configuration"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT command_type, response_message, action_data, is_case_sensitive
            FROM eodb_special_commands_final
            WHERE command = %s AND is_active = TRUE
        """, (command.lower(),))

        result = cur.fetchone()
        if result:
            command_type, response_message, action_data, is_case_sensitive = result
            return {
                "command_type": command_type,
                "response_message": response_message,
                "action_data": json.loads(action_data) if action_data else {},
                "is_case_sensitive": is_case_sensitive
            }
        return None
    except Exception as e:
        logger.error(f"Error getting special command {command}: {e}")
        return None
    finally:
        conn.close()

def get_option_status():
    """Get all option statuses"""
    conn = get_pg_conn()
    cur = conn.cursor()
    try:
        cur.execute("""
            SELECT option_key, option_name, is_enabled, disabled_message
            FROM eodb_option_status_final
            WHERE is_active = TRUE
            ORDER BY order_num
        """)

        results = cur.fetchall()
        return {
            row[0]: {
                "name": row[1],
                "enabled": row[2],
                "disabled_message": row[3]
            }
            for row in results
        }
    except Exception as e:
        logger.error(f"Error getting option status: {e}")
        return {}
    finally:
        conn.close()

# --- Chatbot Endpoints ---

@app.get("/chatbot/config")
async def get_chatbot_config():
    """Get dynamic chatbot configuration for frontend"""
    try:
        # Get greeting messages
        initial_greeting = get_greeting_message("initial_greeting", "Namaskar ! I am your Virtual Assistant for AI Sanlaap !")
        service_help = get_greeting_message("service_help_text", "I can help you with the following services:")
        general_query_prompt = get_greeting_message("general_query_prompt", "If you have any other queries, you may type here ........")
        coming_soon_message = get_greeting_message("coming_soon_message", "This feature is coming soon. Please use the text box below to ask your queries.")

        # Get option status
        option_status = get_option_status()

        # Get system config
        chatbot_name = get_system_config("chatbot_name", "AI Sanlaap")

        return {
            "success": True,
            "config": {
                "chatbot_name": chatbot_name,
                "greeting": {
                    "initial_greeting": initial_greeting,
                    "service_help_text": service_help,
                    "general_query_prompt": general_query_prompt,
                    "coming_soon_message": coming_soon_message
                },
                "options": option_status
            }
        }
    except Exception as e:
        logger.error(f"Error getting chatbot config: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@app.post("/chatbot/step")
async def chatbot_step(req: ChatRequest):
    """
    EoDB chatbot 
    """
    try:
        # Generate session_id if not provided
        if not req.session_id:
            req.session_id = str(uuid.uuid4())

        # Check for navigation actions first (Previous Menu / Main Menu)
        if req.user_input in ["Previous Menu", "Main Menu"]:
            nav_result = handle_navigation_action(req.session_id, req.user_input)
            if nav_result:
                return nav_result

        # Extract values from user_response if provided and store in session
        if req.user_response:
            caption = req.user_response.caption.lower()
            value = req.user_response.value

            # Store the current step's data in session
            if caption == "main_option":
                # Store main option selection
                store_session_data(req.session_id, {"main_option": value})
            elif caption == "sector":
                store_session(req.session_id, value, "")
                store_session_data(req.session_id, {"sector": value})
            elif caption == "investment":
                # Get existing sector and store both
                session_data = get_session(req.session_id)
                sector = session_data.get('sector', '') if session_data else ""
                store_session(req.session_id, sector, value)
                store_session_data(req.session_id, {"investment": value})
            elif caption == "application_type":
                # Store application type in session for CAF flow
                store_session_data(req.session_id, {"application_type": value})
            elif caption == "service_type":
                # Store service type in session
                store_session_data(req.session_id, {"service_type": value})
                logger.info(f"Stored service_type in session: {value}")

        # Get current session data
        session_data = get_session(req.session_id)
        sector = session_data.get('sector', '') if session_data else None
        investment = session_data.get('investment', '') if session_data else None

        # Extract main_option, application_type, service_type, and selected_service from current request or session
        main_option = None
        application_type = None
        service_type = None
        selected_service = None

        # First check current user_response
        if req.user_response:
            caption = req.user_response.caption.lower()
            value = req.user_response.value
            if caption == "main_option":
                main_option = value
            elif caption == "application_type":
                application_type = value
            elif caption == "service_type":
                service_type = value
            elif caption == "selected_service":
                selected_service = value
                # Store selected service in session for context in future queries
                store_session_data(req.session_id, {"selected_service": value})
                store_selected_service(req.session_id, value)

        # If not found in current request, check session data
        if not main_option:
            main_option = get_session_data(req.session_id, "main_option")
        if not application_type:
            application_type = get_session_data(req.session_id, "application_type")
        if not service_type:
            service_type = get_session_data(req.session_id, "service_type")
        if not selected_service:
            selected_service = get_session_data(req.session_id, "selected_service")
        
        # Get service_id from session data
        service_id = get_session_data(req.session_id, "service_id")

        # Determine response_type if not provided
        if not req.response_type:
            # Get general query option dynamically
            general_query_option = get_dynamic_option_by_pattern('application_types', 'general queries')

            # Check if user is in general query mode (step 4 with user_input or general_query caption)
            if req.step == 4 and ((req.user_response and req.user_response.caption.lower() == "general_query") or
                                  (req.user_input and req.user_input.strip()) or
                                  (application_type == general_query_option)):
                req.response_type = "text"
            # Step 6 is for service-specific queries (text input)
            elif req.step == 6 and ((req.user_response and req.user_response.caption.lower() == "service_query") or
                                    (req.user_input and req.user_input.strip())):
                req.response_type = "text"
            # Default to options for steps 1-5
            elif req.step <= 5:
                req.response_type = "options"
            # Step 6 without user input should show prompt
            elif req.step == 6:
                req.response_type = "text"
            else:
                req.response_type = "options"

        # Debug logging
        logger.info(f"Request details - Step: {req.step}, Response Type: {req.response_type}, User Input: '{req.user_input}'")
        logger.info(f"Session data - Services: {get_session_data(req.session_id, 'services') is not None}, CAF: {get_session_data(req.session_id, 'caf_id_no')}, Service ID: {get_session_data(req.session_id, 'service_id')}")

        # PRIORITY: Handle CAF service selection (Step 7) regardless of response_type
        if req.step == 7 and get_session_data(req.session_id, "services") and req.user_input and req.user_input.strip():
            selected_service = req.user_input.strip()
            services = get_session_data(req.session_id, "services")
            caf_id_no = get_session_data(req.session_id, "caf_id_no")
            service_id = get_session_data(req.session_id, "service_id")

            logger.info(f"Step 7 - Service selection: '{selected_service}', CAF: {caf_id_no}, Service ID: {service_id}")
            logger.info(f"Available services: {[s['service_name'] for s in services] if services else 'None'}")

            # Use Silpasathi API service for service selection
            result = await handle_service_selection(selected_service, services, caf_id_no, service_id)

            if result["success"]:
                return {
                    "session_id": req.session_id,
                    "intent_id": "121",
                    "intent_name": "service_status",
                    "response": result["message"],
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": result["step"]
                }
            else:
                # Show services list again
                service_options = [f"{service['service_name']}" for service in services]
                button_list = format_options_as_buttons(service_options)

                return {
                    "session_id": req.session_id,
                    "intent_id": "116",
                    "intent_name": "invalid_service_selection",
                    "response": result["message"],
                    "response_type": "options",
                    "option_list": button_list,
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": result["step"]
                }

        # Handle different response types
        if req.response_type == "text":
            # PRIORITY: Check for special keywords first (exit, restart, redirects)
            if req.user_input and req.user_input.strip():
                user_input = req.user_input.strip()

                # Check for special commands (exit, restart, etc.)
                special_command = get_special_command(user_input)
                if special_command:
                    action_data = special_command.get("action_data", {})

                    if special_command["command_type"] == "exit":
                        return {
                            "session_id": req.session_id,
                            "intent_id": "999",
                            "intent_name": "exit_chatbot",
                            "response": special_command["response_message"],
                            "response_type": "text",
                            "option_list": "NA",
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": action_data.get("step", 0)
                        }

                    elif special_command["command_type"] == "restart":
                        # Clear session data
                        if action_data.get("clear_session", False):
                            clear_session_data(req.session_id)

                        # Get dynamic greeting messages
                        initial_greeting = get_greeting_message("initial_greeting", "Namaskar ! I am your Virtual Assistant for AI Sanlaap !")
                        service_help = get_greeting_message("service_help_text", "I can help you with the following services:")

                        return {
                            "session_id": req.session_id,
                            "intent_id": "101",
                            "intent_name": "restart_chatbot",
                            "response": f"{initial_greeting}<br><br>{service_help}",
                            "response_type": "options",
                            "option_list": format_options_as_buttons(get_master_data_by_type('main_options')),
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": action_data.get("step", 1)
                        }

                # Check for keyword redirects
                keyword_redirect = get_keyword_redirect(user_input)
                if keyword_redirect:
                    # Clear session and redirect
                    clear_session_data(req.session_id)

                    if keyword_redirect["redirect_to_option"]:
                        store_session_data(req.session_id, {"main_option": keyword_redirect["redirect_to_option"]})

                    # Determine response type based on redirect step
                    if keyword_redirect["redirect_to_step"] == 2 and "status" in keyword_redirect["redirect_to_option"].lower():
                        # Status check - go to CAF input
                        return {
                            "session_id": req.session_id,
                            "intent_id": "109",
                            "intent_name": "redirect_to_status",
                            "response": keyword_redirect["redirect_message"],
                            "response_type": "text",
                            "option_list": "NA",
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": 3  # Go to CAF input step
                        }
                    elif keyword_redirect["redirect_to_step"] == 2 and "licence" in keyword_redirect["redirect_to_option"].lower():
                        # Licence/clearance - show service types
                        options = get_master_data_by_type('service_types')
                        button_list = format_options_as_buttons(options)

                        return {
                            "session_id": req.session_id,
                            "intent_id": "102",
                            "intent_name": "redirect_to_licence",
                            "response": keyword_redirect["redirect_message"],
                            "response_type": "options",
                            "option_list": button_list,
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": keyword_redirect["redirect_to_step"]
                        }

            # PRIORITY: Handle CAF number input for new flow (Step 3 with Know application status)
            if req.step == 3 and main_option == "2. Know application status" and req.user_input and req.user_input.strip():
                caf_number = req.user_input.strip().upper()

                # Use Silpasathi API service
                result = await handle_caf_number_input(caf_number)

                if result["success"]:
                    # Store session data
                    store_session_data(req.session_id, result["session_data"])

                    otp_prompt = get_greeting_message("otp_prompt", "Please enter the OTP you received:")
                    return {
                        "session_id": req.session_id,
                        "intent_id": "111",
                        "intent_name": "ask_otp",
                        "response": f"{result['message']}<br><br>{otp_prompt}",
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 4  # Move to step 4 for OTP input
                    }
                else:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "110",
                        "intent_name": "invalid_caf",
                        "response": result["message"],
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 3  # Stay on step 3 to retry CAF number
                    }

            # PRIORITY: Handle OTP verification for new flow (Step 4 with Know application status)
            elif req.step == 4 and main_option == "2. Know application status" and get_session_data(req.session_id, "caf_id_no") and req.user_input and req.user_input.strip():
                entered_otp = req.user_input.strip()
                caf_id_no = get_session_data(req.session_id, "caf_id_no")

                # Use Silpasathi API service for OTP verification
                result = await handle_otp_verification(caf_id_no, entered_otp)

                if result["success"]:
                    services = result["services"]

                    if services:
                        # Store services in session for later use
                        store_session_data(req.session_id, "available_services", services)

                        # Show services as clickable buttons
                        service_options = [f"{service['service_name']}" for service in services]
                        button_list = format_options_as_buttons(service_options)

                        return {
                            "session_id": req.session_id,
                            "intent_id": "114",
                            "intent_name": "show_services",
                            "response": "Please select a service to view its status:",
                            "response_type": "options",
                            "option_list": button_list,
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": 5  # Move to step 5 for service selection
                        }
                    else:
                        return {
                            "session_id": req.session_id,
                            "intent_id": "117",
                            "intent_name": "no_services",
                            "response": result["message"] + "<br><br>You can now ask any general query:",
                            "response_type": "text",
                            "option_list": "NA",
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": 5  # Move to step 5 for general queries
                        }
                else:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "115",
                        "intent_name": "invalid_otp",
                        "response": result["message"],
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 4  # Stay on step 4 to retry OTP
                    }

            # PRIORITY: Check for CAF OTP verification first (Step 6 with CAF flow - old flow)
            elif req.step == 6 and get_session_data(req.session_id, "caf_id_no") and req.user_input and req.user_input.strip():
                entered_otp = req.user_input.strip()
                caf_id_no = get_session_data(req.session_id, "caf_id_no")

                # Use Silpasathi API service for OTP verification
                result = await handle_otp_verification(caf_id_no, entered_otp)

                if result["success"]:
                    services = result["services"]

                    if services:
                        # Store services in session
                        store_session_data(req.session_id, {
                            "services": services,
                            "caf_id_no": caf_id_no,
                            "service_id": service_id
                        })

                        # Create service options for buttons
                        service_options = [f"{service['service_name']}" for service in services]
                        button_list = format_options_as_buttons(service_options)

                        return {
                            "session_id": req.session_id,
                            "intent_id": "114",
                            "intent_name": "show_services",
                            "response": f"Please select a service to view its status:",
                            "response_type": "options",
                            "option_list": button_list,
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": result["step"]
                        }
                    else:
                        return {
                            "session_id": req.session_id,
                            "intent_id": "117",
                            "intent_name": "no_services",
                            "response": result["message"],
                            "response_type": "text",
                            "option_list": "NA",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": result["step"]
                        }
                else:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "115",
                        "intent_name": "invalid_otp",
                        "response": result["message"],
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": result["step"]
                    }

            # Handle text-based responses (general queries and service-specific queries)
            if not req.user_input or not req.user_input.strip():
                # PRIORITY: Handle service selection at step 4 (when user selects a service but hasn't typed a query yet)
                if req.step == 4 and main_option == "1. Apply for licence/clearance" and selected_service:
                    prompt_message = f"<b>Selected Service: {selected_service}</b><br><br>Please enter your query about this service:"
                    return {
                        "session_id": req.session_id,
                        "intent_id": "104",
                        "intent_name": "service_query_prompt",
                        "response": prompt_message,
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 4
                    }

                # Get general query option dynamically
                general_query_option = get_dynamic_option_by_pattern('application_types', 'general queries')

                # If this is the initial prompt for general queries, show the prompt
                if req.step == 4 and application_type == general_query_option:
                    navigation_html = [
                        '''<div><button type="button" class="nav-btn" onclick="sendMessage('Previous Menu')" style="background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); color: black; border: none; padding: 1px 30px; margin: 0 10px; border-radius: 4px; font-size: 14px; cursor: pointer; " title="Previous Menu">⬅</button>''',
                        '''<button type="button" class="nav-btn" onclick="sendMessage('Main Menu')" style="background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); color: black; border: none; padding: 1px 30px; margin: 0 10px; border-radius: 4px; font-size: 14px; cursor: pointer; " title="Main Menu">🏠</button></div>'''
                    ]    
                    button_list = format_options_as_buttons(navigation_html)
                    return {
                        "session_id": req.session_id,
                        "intent_id": "001",
                        "intent_name": "general_query_prompt",
                        "response": "Please enter your query:",
                        "response_type": "options",
                        "option_list": navigation_html,
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }
                # If this is step 6 (service-specific queries), show service query prompt
                elif req.step == 6:
                    # Get the selected service to show in the prompt
                    selected_service = get_session_data(req.session_id, "selected_service")
                    navigation_html = [
                        '''<div><button type="button" class="nav-btn" onclick="sendMessage('Previous Menu')" style="background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); color: black; border: none; padding: 1px 30px; margin: 0 10px; border-radius: 4px; font-size: 14px; cursor: pointer; " title="Previous Menu">⬅</button>''',
                        '''<button type="button" class="nav-btn" onclick="sendMessage('Main Menu')" style="background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); color: black; border: none; padding: 1px 30px; margin: 0 10px; border-radius: 4px; font-size: 14px; cursor: pointer; " title="Main Menu">🏠</button></div>'''
                    ]    
                    button_list = format_options_as_buttons(navigation_html)

                    if selected_service:
                        prompt_message = f"<b>Selected Service: {selected_service}</b><br><br>Please enter your query about this service:"
                    else:
                        prompt_message = "Please enter your query about the selected service:"
                        
                        # navigation_options = ["Previous Menu", "Main Menu"]
                        # button_list = format_options_as_buttons(navigation_html)

                    return {
                        "session_id": req.session_id,
                        "intent_id": "008",
                        "intent_name": "service_query_prompt",
                        "response": prompt_message,
                        "response_type": "options",
                        "option_list": navigation_html,
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }
                else:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "001",
                        "intent_name": "general_query",
                        "response": "Please enter a query.",
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }

            try:
                # Get collection name from frontend or use default
                collection_name = req.collection_name or get_collection_name("1")

                if not collection_name:
                    return {
                        "session_id": req.session_id,
                        "intent_id": "002",
                        "intent_name": "kb_error",
                        "response": "Sorry, I'm having trouble accessing my knowledge base.",
                        "response_type": "text",
                        "option_list": "NA",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": req.step
                    }

                # Get response from AI chatbot
                # For service-specific queries, add context about the selected service
                query_with_context = req.user_input

                # Check if this is a service-specific query and we have a selected service
                if req.step == 6:
                    selected_service = get_session_data(req.session_id, "selected_service")
                    if selected_service:
                        query_with_context = f"Question about {selected_service}: {req.user_input}"
                        logger.info(f"Added service context: {selected_service}")
                    else:
                        logger.warning(f"No selected service found for session {req.session_id}")
                        # Add a fallback context for service queries
                        query_with_context = f"Service query: {req.user_input}"

                logger.info(f"Calling chatbot_response with query: '{query_with_context}'")
                result = chatbot_response(
                    query_with_context,
                    collection_name,
                    req.followup_yes,
                    req.followup_no
                )
                result["response"]=f'''{result["response"]}<br><code>As on 04 March 2025</code><br>'''
                logger.info(f"Result from chatbot_response: {result}")
                print("Results:", result)

                # --- RAG fallback logic ---
                if result.get("intent_name") == "fallback" or not result.get("response") or result.get("response") == "Thank you for your query! Could you please clarify or provide more details so I can assist you effectively?":
                    try:
                        link_data = json_load_rag()
                        rag_res = rag_api(link_data=link_data["silpasathi_links"]["link"], question=req.user_input)
                        if rag_res.get('answer') and len(rag_res['answer']) > 0:
                            if len(rag_res['answer']) > 1:
                                result["response"] = f"{rag_res['answer'][0]}<br><br><code>{rag_res['answer'][1]}</code><br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
                            else:
                                result["response"] = f"{rag_res['answer'][0]}<br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
                        else:
                            result["response"] = "I couldn't find specific information about your query. Please try rephrasing your question or contact support for assistance."
                    except Exception as rag_error:
                        logger.error(f"RAG fallback error: {rag_error}")
                        result["response"] = "I couldn't find specific information about your query. Please try rephrasing your question or contact support for assistance."

                # Format response with service name for service-specific queries
                formatted_response = result["response"]
                if req.step == 6:
                    selected_service = get_session_data(req.session_id, "selected_service")
                    if selected_service:
                        # Add service name at the beginning of the response
                        formatted_response = f"<b>Service: {selected_service}</b><br><br>{result['response']}"
                        logger.info(f"Added service name to response: {selected_service}")

                # Get navigation buttons HTML for frontend
                navigation_buttons = get_navigation_buttons_html(req.step, application_type)

                # Return the AI response (possibly updated with RAG and service name)
                return {
                    "session_id": req.session_id,
                    "intent_id": result.get("id", "004"),
                    "intent_name": "ai_response",
                    "response": formatted_response,
                    "response_type": "text",
                    "option_list": "NA",
                    "navigation_buttons": navigation_buttons,
                    "followup_yes": result.get("followup_yes", "NA"),
                    "followup_no": result.get("followup_no", "NA"),
                    "step": req.step
                }
            except Exception as e:
                logger.error(f"Error in text response: {e}")
                return {
                    "session_id": req.session_id,
                    "intent_id": "005",
                    "intent_name": "error",
                    "response": "Sorry, I encountered an error. Please try again.",
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": req.step
                }

        # Handle options-based responses
        if req.response_type == "options":
            # Step 1: Choose main option
            if req.step == 1:
                options = get_options_by_step(1)
                button_list = format_options_as_buttons(options)

                # Get dynamic greeting messages
                initial_greeting = get_greeting_message("initial_greeting", "Namaskar ! I am your Virtual Assistant for AI Sanlaap !")
                service_help = get_greeting_message("service_help_text", "I can help you with the following services:")
                caption = f"{initial_greeting}<br><br>{service_help}"

                return {
                    "session_id": req.session_id,
                    "intent_id": "101",
                    "intent_name": "choose_main_option",
                    "response": caption,
                    "response_type": "options",
                    "option_list": button_list,
                    "navigation_buttons": "",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": 1
                }

            # Step 2: Handle different flows based on main option selection
            elif req.step == 2 and main_option:
                logger.info(f"Step 2 processing - main_option: {main_option}")
                if main_option == "1. Apply for licence/clearance":
                    # Show service types (Pre-establishment/Pre-operational)
                    options = get_options_by_step(2, 1, main_option)
                    if not options:  # Fallback
                        options = get_master_data_by_type('service_types')
                    button_list = format_options_as_buttons(options)
                    caption = get_step_caption(2, 1, main_option) or "Select application type:"
                    logger.info(f"Step 2 - Returning service types: {options}")
                    return {
                        "session_id": req.session_id,
                        "intent_id": "102",
                        "intent_name": "choose_service_type",
                        "response": caption,
                        "response_type": "options",
                        "option_list": button_list,
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 2
                    }
                elif main_option == "2. Know application status":
                    # Go directly to CAF number input
                    caf_prompt = get_greeting_message("caf_prompt", "Please enter your CAF number:")
                    return {
                        "session_id": req.session_id,
                        "intent_id": "109",
                        "intent_name": "ask_caf_number",
                        "response": caf_prompt,
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 3  # Move to step 3 for CAF number input
                    }

            # Step 3: Handle different flows based on previous selections
            elif req.step == 3:
                logger.info(f"Step 3 processing - main_option: {main_option}, service_type: {service_type}")
                if main_option == "1. Apply for licence/clearance" and service_type:
                    # Show specific services based on service type selection
                    if service_type == "1. Pre-establishment":
                        options = get_options_by_step(3, 2, service_type)
                        if not options:  # Fallback
                            options = get_master_data_by_type('pre_establishment')
                        button_list = format_options_as_buttons(options)
                        caption = get_step_caption(3, 2, service_type) or "Select the Pre-establishment service:"
                        intent_name = "choose_pre_establishment"
                        logger.info(f"Step 3 - Pre-establishment services: {len(options)} options")
                    elif service_type == "2. Pre-operational":
                        options = get_options_by_step(3, 2, service_type)
                        if not options:  # Fallback
                            options = get_master_data_by_type('pre_operation')
                        button_list = format_options_as_buttons(options)
                        caption = get_step_caption(3, 2, service_type) or "Select the Pre-operation service:"
                        intent_name = "choose_pre_operation"
                        logger.info(f"Step 3 - Pre-operational services: {len(options)} options")
                    else:
                        # Default fallback
                        options = get_master_data_by_type('service_types')
                        button_list = format_options_as_buttons(options)
                        caption = "Please select a service type:"
                        intent_name = "choose_service_type"

                    return {
                        "session_id": req.session_id,
                        "intent_id": "103",
                        "intent_name": intent_name,
                        "response": caption,
                        "response_type": "options",
                        "option_list": button_list,
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 3
                    }
                elif main_option == "2. Know application status":
                    # This step should handle CAF number input via text response
                    # If we reach here, show CAF number prompt
                    caf_prompt = get_greeting_message("caf_prompt", "Please enter your CAF number:")
                    return {
                        "session_id": req.session_id,
                        "intent_id": "109",
                        "intent_name": "ask_caf_number",
                        "response": caf_prompt,
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 3
                    }

            # Step 4: Handle different flows based on main option and previous selections
            elif req.step == 4:
                if main_option == "1. Apply for licence/clearance":
                    # After service selection, enable text input for queries about the selected service
                    if not selected_service:
                        selected_service = get_session_data(req.session_id, "selected_service")

                    if selected_service:
                        prompt_message = f"<b>Selected Service: {selected_service}</b><br><br>Please enter your query about this service:"
                    else:
                        prompt_message = "Please enter your query about the selected service:"

                    return {
                        "session_id": req.session_id,
                        "intent_id": "104",
                        "intent_name": "service_query_prompt",
                        "response": prompt_message,
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 4
                    }

                elif main_option == "2. Know application status":
                    # Handle OTP input after CAF number verification
                    otp_prompt = get_greeting_message("otp_prompt", "Please enter the OTP you received:")
                    return {
                        "session_id": req.session_id,
                        "intent_id": "111",
                        "intent_name": "ask_otp",
                        "response": otp_prompt,
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 4
                    }



            # Step 5: Handle service selection after OTP verification
            elif req.step == 5:
                if main_option == "2. Know application status":
                    # Handle service selection from button click
                    if req.user_response and req.user_response.value:
                        selected_service_name = req.user_response.value
                        available_services = get_session_data(req.session_id, "available_services")
                        caf_id_no = get_session_data(req.session_id, "caf_id_no")

                        if available_services and caf_id_no:
                            # Find the selected service
                            selected_service = None
                            for service in available_services:
                                if service['service_name'] == selected_service_name:
                                    selected_service = service
                                    break

                            if selected_service:
                                # Get detailed status for the selected service
                                result = await handle_service_selection(
                                    selected_service_name,
                                    available_services,
                                    caf_id_no,
                                    selected_service['service_id']
                                )

                                if result["success"]:
                                    return {
                                        "session_id": req.session_id,
                                        "intent_id": "116",
                                        "intent_name": "service_status",
                                        "response": result["message"],
                                        "response_type": "text",
                                        "option_list": "NA",
                                        "navigation_buttons": "",
                                        "followup_yes": "NA",
                                        "followup_no": "NA",
                                        "step": 6  # Move to general query step
                                    }
                                else:
                                    return {
                                        "session_id": req.session_id,
                                        "intent_id": "117",
                                        "intent_name": "service_status_error",
                                        "response": result["message"],
                                        "response_type": "text",
                                        "option_list": "NA",
                                        "navigation_buttons": "",
                                        "followup_yes": "NA",
                                        "followup_no": "NA",
                                        "step": 5  # Stay on same step
                                    }
                            else:
                                return {
                                    "session_id": req.session_id,
                                    "intent_id": "118",
                                    "intent_name": "service_not_found",
                                    "response": "Service not found. Please select a valid service.",
                                    "response_type": "text",
                                    "option_list": "NA",
                                    "navigation_buttons": "",
                                    "followup_yes": "NA",
                                    "followup_no": "NA",
                                    "step": 5  # Stay on same step
                                }
                        else:
                            return {
                                "session_id": req.session_id,
                                "intent_id": "119",
                                "intent_name": "session_data_missing",
                                "response": "Session data missing. Please restart the process.",
                                "response_type": "text",
                                "option_list": "NA",
                                "navigation_buttons": "",
                                "followup_yes": "NA",
                                "followup_no": "NA",
                                "step": 2  # Go back to start
                            }
                    else:
                        # If no service selected, show the service list again
                        available_services = get_session_data(req.session_id, "available_services")
                        if available_services:
                            service_options = [f"{service['service_name']}" for service in available_services]
                            button_list = format_options_as_buttons(service_options)

                            return {
                                "session_id": req.session_id,
                                "intent_id": "114",
                                "intent_name": "show_services",
                                "response": "Please select a service to view its status:",
                                "response_type": "options",
                                "option_list": button_list,
                                "navigation_buttons": "",
                                "followup_yes": "NA",
                                "followup_no": "NA",
                                "step": 5
                            }
                        else:
                            return {
                                "session_id": req.session_id,
                                "intent_id": "119",
                                "intent_name": "session_data_missing",
                                "response": "Session data missing. Please restart the process.",
                                "response_type": "text",
                                "option_list": "NA",
                                "navigation_buttons": "",
                                "followup_yes": "NA",
                                "followup_no": "NA",
                                "step": 2  # Go back to start
                            }



            # Step 6: Handle general queries after viewing application status
            elif req.step == 6:
                if main_option == "2. Know application status":
                    # After viewing application status, allow general queries
                    return {
                        "session_id": req.session_id,
                        "intent_id": "112",
                        "intent_name": "post_status_query_prompt",
                        "response": "You can now ask any general query about your application:",
                        "response_type": "text",
                        "option_list": "NA",
                        "navigation_buttons": "",
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 6
                    }

            # Step 8: Final step - service status has been shown
            elif req.step == 8:
                if req.user_input and req.user_input.strip():
                    try:
                        # Use the same logic as step 6: first try vectordb, then fallback to RAG
                        query_with_context = req.user_input
                        collection_name = req.collection_name or get_collection_name("1") 
                        result = chatbot_response(
                            query_with_context,
                            collection_name,
                            req.followup_yes,
                            req.followup_no
                        )
                        # --- RAG fallback logic ---
                        if result.get("intent_name") == "fallback" or not result.get("response") or result.get("response") == "Thank you for your query! Could you please clarify or provide more details so I can assist you effectively?":
                            try:
                                link_data = json_load_rag()
                                rag_res = rag_api(link_data=link_data["silpasathi_links"]["link"], question=req.user_input)
                                if rag_res.get('answer') and len(rag_res['answer']) > 0:
                                    if len(rag_res['answer']) > 1:
                                        result["response"] = f"{rag_res['answer'][0]}<br><br><code>{rag_res['answer'][1]}</code><br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
                                    else:
                                        result["response"] = f"{rag_res['answer'][0]}<br><br><b>Disclaimer: This is an AI generated Response. Please verify it from authentic sources.</b>"
                                else:
                                    result["response"] = "I couldn't find specific information about your query. Please try rephrasing your question or contact support for assistance."
                            except Exception as rag_error:
                                logger.error(f"RAG fallback error: {rag_error}")
                                result["response"] = "I couldn't find specific information about your query. Please try rephrasing your question or contact support for assistance."
                        #############################################################
                        formatted_response = result["response"] = f'''{result["response"]}<br><code>As on 04 March 2023</code><br>'''
                        #navigation_buttons = get_navigation_buttons_html(req.step, application_type)
                        return {
                            "session_id": req.session_id,
                            "intent_id": result.get("id", "004"),
                            "intent_name": "ai_response",
                            "response": formatted_response,
                            "response_type": "text",
                            "option_list": "NA",
                            "followup_yes": result.get("followup_yes", "NA"),
                            "followup_no": result.get("followup_no", "NA"),
                            "step": req.step
                        }
                    except Exception as e:
                        logger.error(f"Error in step 8 text response: {e}")
                        return {
                            "session_id": req.session_id,
                            "intent_id": "999",
                            "intent_name": "error",
                            "response": "An error occurred while processing your query. Please try again later.",
                            "response_type": "text",
                            "option_list": "NA",
                            "navigation_buttons": "",
                            "followup_yes": "NA",
                            "followup_no": "NA",
                            "step": req.step
                        }
                else:
                    navigation_html = [
                        '''<div><button type="button" class="nav-btn" onclick="sendMessage('Previous Menu')" style="background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); color: black; border: none; padding: 1px 30px; margin: 0 10px; border-radius: 4px; font-size: 14px; cursor: pointer; " title="Previous Menu">⬅</button>''',
                        '''<button type="button" class="nav-btn" onclick="sendMessage('Main Menu')" style="background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); color: black; border: none; padding: 1px 30px; margin: 0 10px; border-radius: 4px; font-size: 14px; cursor: pointer; " title="Main Menu">🏠</button></div>'''
                    ]    
                    #button_list = format_options_as_buttons(navigation_html)
                    return {
                        "session_id": req.session_id,
                        "intent_id": "122",
                        "intent_name": "status_complete",
                        "response": "Status inquiry completed. Now you ask queries.",
                        "response_type": "options",
                        "option_list": navigation_html,
                        "followup_yes": "NA",
                        "followup_no": "NA",
                        "step": 8
                    }

            # Invalid step or missing data
            else:
                return {
                    "session_id": req.session_id,
                    "intent_id": "108",
                    "intent_name": "invalid_step",
                    "response": "Invalid step or missing data.",
                    "response_type": "text",
                    "option_list": "NA",
                    "followup_yes": "NA",
                    "followup_no": "NA",
                    "step": req.step
                }


        # Handle other response types if needed
        else:
            return {
                "session_id": req.session_id,
                "intent_id": "999",
                "intent_name": "unsupported_type",
                "response": "Unsupported response type.",
                "response_type": "text",
                "option_list": "NA",
                "followup_yes": "NA",
                "followup_no": "NA",
                "step": req.step
            }

    except Exception as e:
        logger.error(f"Error in chatbot_step: {e}")
        return {
            "session_id": req.session_id if req.session_id else str(uuid.uuid4()),
            "intent_id": "500",
            "intent_name": "error",
            "response": "An error occurred while processing your request.",
            "response_type": "text",
            "option_list": "NA",
            "followup_yes": "NA",
            "followup_no": "NA",
            "step": req.step if req.step else 1
        }

# --- Session Management Endpoint ---
@app.post("/session_manager")
async def session_manager(session_params: SessionIdGenerator):
    timestamp = datetime.datetime.now(tz)
    session_type = session_params.session_type
    session_id = session_params.session_id

    try:
        if session_type == "session_create":
            user_session_id = str(uuid.uuid4())
            # Initialize session in database
            store_session(user_session_id, "", "")

            result = {
                "status": "Session Created!",
                "session_id": user_session_id,
                "time_stamp": timestamp,
            }
        elif session_type == "session_destroy" and session_id:
            # Clear session data
            clear_all_session_data(session_id)
            result = {
                "status": "Session Destroyed!",
                "session_id": session_id,
                "time_stamp": timestamp,
            }
        else:
            result = {
                "status": "Invalid session operation",
                "time_stamp": timestamp,
            }

        return result

    except Exception as e:
        logger.error(f"Error processing session request: {str(e)}")
        raise HTTPException(status_code=500, detail="An error occurred while processing the request.")

# --- Server startup ---
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8020)
