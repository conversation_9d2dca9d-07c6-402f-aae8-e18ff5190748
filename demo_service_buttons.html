<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EoDB Chatbot - Service Button Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chat-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-messages {
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 18px;
            max-width: 80%;
        }
        .operator {
            background-color: #e3f2fd;
            margin-right: auto;
        }
        .visitor {
            background-color: #4fc3f7;
            color: white;
            margin-left: auto;
        }
        .options-container {
            margin-top: 10px;
        }
        .option_btn {
            display: block;
            width: 100%;
            margin: 8px 0;
            padding: 12px 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-align: left;
        }
        .option_btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        .status-details {
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status-under-review {
            color: orange;
            font-weight: bold;
        }
        .demo-step {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h2>🤖 AI Sanlaap (EoDB)</h2>
            <p>Service Button Demo</p>
        </div>
        
        <div class="chat-messages">
            <div class="demo-step">
                Step 1: User enters CAF number and OTP
            </div>
            
            <div class="message visitor">
                CAF2024000123
            </div>
            
            <div class="message operator">
                OTP sent to your registered mobile number: 70******38<br>
                Please enter the OTP you received:
            </div>
            
            <div class="message visitor">
                123456
            </div>
            
            <div class="demo-step">
                Step 2: After OTP verification - Show services as buttons
            </div>
            
            <div class="message operator">
                Please select a service to view its status:
                <div class="options-container">
                    <button type="button" class="option_btn" onclick="selectService('Registration of Motor Transport undertaking under Motor Transport Workers Act, 1961')">
                        Registration of Motor Transport undertaking under Motor Transport Workers Act, 1961
                    </button>
                    <button type="button" class="option_btn" onclick="selectService('Registration of Principal Employer\'s under The Contracts Labour (Regulation and Abolition) Act, 1970')">
                        Registration of Principal Employer's under The Contracts Labour (Regulation and Abolition) Act, 1970
                    </button>
                </div>
            </div>
            
            <div class="demo-step">
                Step 3: User clicks on a service button
            </div>
            
            <div class="message visitor" id="selected-service" style="display: none;">
                Registration of Motor Transport undertaking under Motor Transport Workers Act, 1961
            </div>
            
            <div class="message operator" id="service-status" style="display: none;">
                <div class="status-details">
                    <b>Service Status Details:</b><br><br>
                    <b>CAF Number:</b> CAF2024000123<br>
                    <b>Service Name:</b> Registration of Motor Transport undertaking under Motor Transport Workers Act, 1961<br>
                    <b>Department:</b> Labour Department<br>
                    <b>Current Status:</b> <span class="status-under-review">Under Review</span><br><br>
                    <b>Status Description:</b><br>
                    Your application is currently under review by the department. You will be notified once the review is complete.<br><br>
                    <i>For more details, please contact Labour Department or visit the Silpasathi portal.</i>
                </div>
            </div>
            
            <div class="message operator" id="general-query-prompt" style="display: none;">
                You can now ask any general query about your application:
            </div>
        </div>
    </div>

    <script>
        function selectService(serviceName) {
            // Show user selection
            document.getElementById('selected-service').style.display = 'block';
            document.getElementById('selected-service').textContent = serviceName;
            
            // Show service status after a short delay
            setTimeout(() => {
                document.getElementById('service-status').style.display = 'block';
                
                // Show general query prompt after another delay
                setTimeout(() => {
                    document.getElementById('general-query-prompt').style.display = 'block';
                }, 1000);
            }, 500);
            
            // Disable buttons after selection
            const buttons = document.querySelectorAll('.option_btn');
            buttons.forEach(btn => {
                btn.disabled = true;
                btn.style.opacity = '0.5';
                btn.style.cursor = 'not-allowed';
            });
        }
    </script>
</body>
</html>
